{"authors": [{"maintainer": true, "name": "<PERSON>", "url": "https://github.com/Links2004"}], "description": "WebSocket Server and Client for Arduino based on RFC6455", "export": {"exclude": ["tests"]}, "frameworks": "a<PERSON><PERSON><PERSON>", "keywords": "wifi, http, web, server, client, websocket", "license": "LGPL-2.1", "name": "WebSockets", "platforms": "<PERSON><PERSON><PERSON><PERSON>, espressif8266, espressif32, raspberry<PERSON>", "repository": {"type": "git", "url": "https://github.com/Links2004/arduinoWebSockets.git"}, "version": "2.4.1"}