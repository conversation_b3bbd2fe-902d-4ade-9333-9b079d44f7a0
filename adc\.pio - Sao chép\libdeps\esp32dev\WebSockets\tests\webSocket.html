<html>
<head>

<script>
var connection = new WebSocket('ws://*********:81/', ['arduino']);

connection.onopen = function () {
	connection.send('Message from <PERSON>rowser to ESP8266 yay its Working!! ' + new Date()); 
	connection.send('ping');
	
/*	setInterval(function() {
		connection.send('Time: ' + new Date()); 
	}, 20);
*/
connection.send('Time: ' + new Date()); 
};

connection.onerror = function (error) {
	console.log('WebSocket Error ', error);
};

connection.onmessage = function (e) {
	console.log('Server: ', e.data);
	connection.send('Time: ' + new Date()); 
};

function sendRGB() {
	var r = parseInt(document.getElementById('r').value).toString(16);
	var g = parseInt(document.getElementById('g').value).toString(16);
	var b = parseInt(document.getElementById('b').value).toString(16);
	if(r.length < 2) { r = '0' + r; }
	if(g.length < 2) { g = '0' + g; }
	if(b.length < 2) { b = '0' + b; }
	var rgb = '#'+r+g+b;
	console.log('RGB: ' + rgb);
	connection.send(rgb); 
}

</script>

</head>
<body>
LED Control:<br/>
<br/>
R: <input id="r" type="range" min="0" max="255" step="1" onchange="sendRGB();" /><br/>
G: <input id="g" type="range" min="0" max="255" step="1" onchange="sendRGB();" /><br/>
B: <input id="b" type="range" min="0" max="255" step="1" onchange="sendRGB();" /><br/>
</body>
</html>