name: CI
on:
  schedule:
    - cron: '0 0 * * 5'
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
  release:
    types: [ published, created, edited ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  check_version_files:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: check version
        run: |
          $GITHUB_WORKSPACE/travis/version.py --check

  prepare_example_json:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: generate examples
        id: set-matrix
        run: |
          source $GITHUB_WORKSPACE/travis/common.sh
          cd $GITHUB_WORKSPACE

          echo -en "matrix=" >> $GITHUB_OUTPUT
          echo -en "[" >> $GITHUB_OUTPUT

          get_sketches_json_matrix arduino $GITHUB_WORKSPACE/examples/esp8266_pico esp8266 0.35.0 esp8266:esp8266:generic:xtal=80,vt=flash,exception=disabled,stacksmash=disabled,ssl=all,mmu=3232,non32xfer=fast,ResetMethod=nodemcu,CrystalFreq=26,FlashFreq=80,FlashMode=qio,eesz=4M2M,led=2,sdk=nonosdk_190703,ip=lm2f,dbg=Serial1,lvl=SSL,wipe=none,baud=115200 >> $GITHUB_OUTPUT
          echo -en "," >> $GITHUB_OUTPUT

          get_sketches_json_matrix arduino $GITHUB_WORKSPACE/examples/esp8266_pico esp8266 0.35.0 esp8266:esp8266:generic:xtal=80,vt=flash,exception=disabled,stacksmash=disabled,ssl=all,mmu=3232,non32xfer=fast,ResetMethod=nodemcu,CrystalFreq=26,FlashFreq=80,FlashMode=qio,eesz=4M2M,led=2,sdk=nonosdk_190703,ip=lm2f,dbg=Disabled,lvl=None____,wipe=none,baud=115200 >> $GITHUB_OUTPUT
          echo -en "," >> $GITHUB_OUTPUT

          get_sketches_json_matrix arduino $GITHUB_WORKSPACE/examples/esp32 esp32 0.35.0 esp32:esp32:esp32:FlashFreq=80 >> $GITHUB_OUTPUT

          echo -en "]" >> $GITHUB_OUTPUT
          echo >> $GITHUB_OUTPUT
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}

  prepare_ide:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        CLI_VERSION: [0.35.0]
    env:
      CLI_VERSION: ${{ matrix.CLI_VERSION }}
      ARDUINO_DIRECTORIES_DATA: /home/<USER>/arduino_ide

    steps:
      - uses: actions/checkout@v2

      - name: Get Date
        id: get-date
        run: |
          echo "date=$(/bin/date -u "+%Y%m%d")" >> $GITHUB_OUTPUT
        shell: bash

      - uses: actions/cache@v3
        id: cache_all
        with:
          path: |
            /home/<USER>/arduino_ide
            /home/<USER>/Arduino
          key: ${{ runner.os }}-${{ steps.get-date.outputs.date }}-${{ matrix.CLI_VERSION }}-cli

      - name: download IDE
        if: steps.cache_all.outputs.cache-hit != 'true'
        run: |
          wget https://github.com/arduino/arduino-cli/releases/download/v${CLI_VERSION}/arduino-cli_${CLI_VERSION}_Linux_64bit.tar.gz -q
          tar xf arduino-cli_${CLI_VERSION}_Linux_64bit.tar.gz
          mkdir -p $ARDUINO_DIRECTORIES_DATA
          mv arduino-cli $ARDUINO_DIRECTORIES_DATA/

      - name: download ArduinoJson
        if: steps.cache_all.outputs.cache-hit != 'true'
        run: |
          mkdir -p $HOME/Arduino/libraries
          wget https://github.com/bblanchon/ArduinoJson/archive/6.x.zip -q
          unzip 6.x.zip
          mv ArduinoJson-6.x $HOME/Arduino/libraries/ArduinoJson

      - name: download cores
        if: steps.cache_all.outputs.cache-hit != 'true'
        run: |
          export PATH="$ARDUINO_DIRECTORIES_DATA:$PATH"
          source $GITHUB_WORKSPACE/travis/common.sh
          get_core_cli

  build:
    needs: [prepare_ide, prepare_example_json]
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        include: ${{ fromJson(needs.prepare_example_json.outputs.matrix) }}
    env:
      CPU: ${{ matrix.cpu }}
      BOARD: ${{ matrix.board }}
      CLI_VERSION: ${{ matrix.cliversion }}
      SKETCH: ${{ matrix.sketch }}
      ARDUINO_DIRECTORIES_DATA: /home/<USER>/arduino_ide

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - uses: actions/checkout@v2

      - name: install libgtk2.0-0
        run: |
          sudo apt-get install -y libgtk2.0-0

      - name: Get Date
        id: get-date
        run: |
          echo "date=$(/bin/date -u "+%Y%m%d")" >> $GITHUB_OUTPUT
        shell: bash

      - uses: actions/cache@v3
        id: cache_all
        with:
          path: |
            /home/<USER>/arduino_ide
            /home/<USER>/Arduino
          key: ${{ runner.os }}-${{ steps.get-date.outputs.date }}-${{ matrix.cliversion }}-cli

      - name: install python serial
        if: matrix.cpu == 'esp32'
        run: |
          sudo pip3 install pyserial
          sudo pip install pyserial
#          sudo apt install python-is-python3

      - name: test IDE
        run: |
          export PATH="$ARDUINO_DIRECTORIES_DATA:$PATH"
          which arduino-cli

      - name: copy code
        run: |
          mkdir -p $HOME/Arduino/libraries/
          cp -r $GITHUB_WORKSPACE $HOME/Arduino/libraries/arduinoWebSockets

      - name: build example
        timeout-minutes: 20
        run: |
          set -ex
          export PATH="$HOME/arduino_ide:$PATH"
          source $GITHUB_WORKSPACE/travis/common.sh
          cd $GITHUB_WORKSPACE
          build_sketch_cli "$SKETCH" "$BOARD"

  done:
    needs: [prepare_ide, prepare_example_json, build, check_version_files]
    runs-on: ubuntu-latest
    steps:
      - name: Done
        run: |
          echo DONE
