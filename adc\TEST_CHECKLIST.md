# ADS1220 Migration Test Checklist

## Pre-Test Hardware Setup
- [ ] Connect ADS1220 module to ESP32 VSPI pins
- [ ] Connect DRDY pin (ADS1220) to GPIO 4
- [ ] Connect 5kΩ shunt resistor between A0-A1
- [ ] Connect thermocouple to A2-A3
- [ ] Add RC filters (100Ω + 0.1µF) to each input channel
- [ ] Verify 3.3V power supply to ADS1220

## SPI Communication Test
- [ ] Upload code and check Serial Monitor
- [ ] Verify "[ADS1220] Khởi tạo thành công!" message
- [ ] Check calibration test voltage (~825mV expected)
- [ ] Confirm no SPI communication errors

## Measurement Accuracy Test

### Current Measurement (A0-A1)
- [ ] Connect multimeter in series with 5kΩ shunt
- [ ] Apply known current (10µA, 25µA, 50µA)
- [ ] Compare ESP32 readings with multimeter
- [ ] Verify readings within ±2% of multimeter
- [ ] Check noise level < 0.1µA RMS

### Thermocouple Measurement (A2-A3)  
- [ ] Connect precision voltage source (5mV, 15mV, 30mV)
- [ ] Compare ESP32 readings with multimeter
- [ ] Verify readings within ±0.1mV of multimeter
- [ ] Check temperature coefficient stability

## Performance Test
- [ ] Verify 20 SPS sampling rate
- [ ] Check 50/60Hz noise rejection
- [ ] Measure conversion time (~50ms per sample)
- [ ] Test real-time vs high-accuracy modes
- [ ] Verify MovingAverageFilter performance

## Web Interface Compatibility
- [ ] Connect to WiFi successfully
- [ ] WebSocket connection established
- [ ] Real-time data updates (1Hz)
- [ ] Data logging updates (0.1Hz)
- [ ] Chart display working correctly
- [ ] Control buttons functional

## Stress Test
- [ ] Run continuously for 1 hour
- [ ] Monitor memory usage (heap > 10KB)
- [ ] Check for measurement drift
- [ ] Verify no WebSocket disconnections
- [ ] Test power cycle recovery

## Expected Performance Metrics
- **Resolution**: ~0.00095µV/bit @ gain 128
- **Current Range**: 0-50µA (0-250µV across 5kΩ)
- **Thermocouple Range**: 0-30mV
- **Accuracy**: ±0.1% of reading
- **Noise**: < 0.1µA RMS current, < 0.01mV RMS voltage
- **Update Rate**: Real-time 1Hz, Logging 0.1Hz

## Troubleshooting Guide

### SPI Communication Issues
- Check wiring: MOSI=23, MISO=19, SCLK=18, CS=5
- Verify DRDY connection to GPIO 4
- Check 3.3V power supply
- Ensure proper ground connections

### Measurement Issues
- Verify shunt resistor value (5kΩ ±1%)
- Check RC filter components (100Ω + 0.1µF)
- Ensure proper differential connections
- Check for EMI interference

### Performance Issues
- Monitor Serial output for error messages
- Check heap memory usage
- Verify 20 SPS timing
- Test with known reference voltages
