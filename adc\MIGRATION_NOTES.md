# ADS1220 Migration Notes

## Migration Summary
Successfully migrated from ADS1115 (16-bit I2C) to ADS1220 (24-bit SPI) for ESP32 Curie experiment system.

## Hardware Configuration

### ESP32 SPI Pins (VSPI)
- **MOSI**: GPIO 23 (default)
- **MISO**: GPIO 19 (default) 
- **SCLK**: GPIO 18 (default)
- **CS**: GPIO 5 (ADS1220_CS_PIN)
- **DRDY**: GPIO 4 (ADS1220_DRDY_PIN) - Data Ready detection

### ADS1220 Configuration
- **Reference**: Internal 2.048V
- **Data Rate**: 20 SPS (50/60Hz rejection)
- **Mode**: Single-shot conversion
- **Current Measurement (A0-A1)**: PGA gain 128 (±16mV range)
- **Thermocouple (A2-A3)**: PGA gain 64 (±32mV range)

## Key Changes Made

### 1. Library & Includes
```cpp
// OLD: ADS1115
#include <Wire.h>
#include <Adafruit_ADS1X15.h>
Adafruit_ADS1115 ads;

// NEW: ADS1220
#include <SPI.h>
#include <ADS1220_WE.h>
ADS1220_WE ads = ADS1220_WE(ADS1220_CS_PIN, ADS1220_DRDY_PIN);
```

### 2. Measurement Function
- **Timing**: Adjusted for 20 SPS (50ms per conversion)
- **Samples**: Reduced to 4-8 samples (vs 8-16 for ADS1115)
- **Resolution**: Improved from 7.8125µV/bit to ~0.00095µV/bit @ gain 128
- **Range**: Current 0-250µV, Thermocouple 0-30mV

### 3. Initialization
```cpp
ads.init();
ads.setVRefSource(ADS1220_VREF_INT);
ads.setDataRate(ADS1220_DR_20_SPS);
ads.setOperatingMode(ADS1220_SINGLE_SHOT);
```

## Performance Improvements
- **Resolution**: 256x better (24-bit vs 16-bit)
- **Noise**: Better 50/60Hz rejection at 20 SPS
- **Accuracy**: Closer to multimeter readings
- **Stability**: Internal voltage reference

## Preserved Features
- MovingAverageFilter class (unchanged)
- Dual data streams (realtime vs logging)
- WebSocket communication (unchanged)
- GPIO control logic (unchanged)
- Outlier rejection and adaptive filtering

## Testing Required
1. Verify SPI communication
2. Check measurement accuracy vs multimeter
3. Validate noise performance
4. Test web interface compatibility
5. Confirm timing performance
