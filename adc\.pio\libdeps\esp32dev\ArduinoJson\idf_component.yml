version: "6.21.4"
description: >-
  A simple and efficient JSON library for embedded C++.
  ArduinoJson supports ✔ serialization, ✔ deserialization, ✔ MessagePack, ✔ fixed allocation, ✔ zero-copy, ✔ streams, ✔ filtering, and more.
  It is the most popular Arduino library on GitHub ❤❤❤❤❤.
  Check out arduinojson.org for a comprehensive documentation.
url: https://arduinojson.org/
files:
  exclude:
    - "**/.vs/**/*"
    - ".devcontainer/**/*"
    - "examples/**/*"
    - "extras/**/*"
