# Macros
JSON_ARRAY_SIZE	KEYWORD2
JSON_OBJECT_SIZE	KEYWORD2
JSON_STRING_SIZE	KEYWORD2

# Free functions
deserializeJson	KEYWORD2
deserializeMsgPack	KEYWORD2
serialized	KEY<PERSON>ORD2
serializeJson	KEYWORD2
serializeJsonPretty	KEYWORD2
serializeMsgPack	KEYWORD2
measureJson	KEYWORD2
measureJsonPretty	KEYWORD2
measureMsgPack	KEYWORD2

# Methods
add	KEYWORD2
as	KEYWORD2
createNestedArray	KEYWORD2
createNestedObject	KEYWORD2
get	KEYWORD2
set	KEYWORD2
to	KEYWORD2

# Type names
DeserializationError	KEYWORD1	DATA_TYPE
DynamicJsonDocument	KEYWORD1	DATA_TYPE
JsonArray	KEYWORD1	DATA_TYPE
JsonArrayConst	KEYWORD1	DATA_TYPE
JsonDocument	KEYWORD1	DATA_TYPE
JsonFloat	KEYWORD1	DATA_TYPE
JsonInteger	KEYWORD1	DATA_TYPE
JsonObject	KEYWORD1	DATA_TYPE
JsonObjectConst	KEYWORD1	DATA_TYPE
JsonString	KEYWORD1	DATA_TYPE
JsonUInt	KEYWORD1	DATA_TYPE
JsonVariant	KEYWORD1	DATA_TYPE
JsonVariantConst	KEYWORD1	DATA_TYPE
StaticJsonDocument	KEYWORD1	DATA_TYPE
