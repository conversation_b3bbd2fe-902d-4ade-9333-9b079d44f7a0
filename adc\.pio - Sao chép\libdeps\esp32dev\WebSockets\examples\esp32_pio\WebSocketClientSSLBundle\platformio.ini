; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
upload_speed = 921600
build_flags = 
    -DCORE_DEBUG_LEVEL=5
lib_deps = ../../../src

extra_scripts = 
    pre:run_gen_script.py

board_build.embed_txtfiles = 
    data/cert/x509_crt_bundle.bin
